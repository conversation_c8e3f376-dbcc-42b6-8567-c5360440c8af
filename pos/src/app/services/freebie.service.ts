import { Injectable } from '@angular/core';
import { FreebieProduct, FreebieCartItem } from '../models/freebie.model';
import { CartItem } from '../models/product.model';
import { TypeSenseService } from './typesense.service';

@Injectable({
  providedIn: 'root'
})
export class FreebieService {

  constructor(private typesenseService: TypeSenseService) {}

  //Get available freebies for the given cart amount
  
  async getAvailableFreebies(cartAmount: number): Promise<FreebieProduct[]> {
    try {
      const freebies = await this.typesenseService.getFreebiesProducts(cartAmount);
      return freebies?.[0] ? [freebies[0]] : []; // Return only highest qualifying freebie
    } catch (error) {
      console.error('Error fetching freebies:', error);
      return [];
    }
  }
  //Check if a freebie is already in the cart
  isFreebieInCart(cartItems: CartItem[], freebieId: string): boolean {
    return cartItems.some((item: any) => item.is_freebie && item.freebie_id === freebieId);
  }

  // Get current freebie in cart
  getCurrentFreebieInCart(cartItems: CartItem[]): any {
    return cartItems.find((item: any) => item.is_freebie);
  }

  //Create freebie cart item
  createFreebieCartItem(freebie: FreebieProduct): FreebieCartItem {
    return {
      id: `freebie_${freebie.id}`,
      name: freebie.name,
      child_sku: freebie.child_sku || freebie.sku,
      selling_price: 0,
      quantity: 1,
      tax: 0, cgst: 0, sgst: 0, igst: 0, cess: 0,
      taxable: false,
      thumbnail_image: freebie.thumbnail_image,
      variant_name: freebie.variant_name,
      is_freebie: true,
      freebie_id: freebie.id,
      freebie_amount: freebie.amount,
      freebie_name: freebie.name
    } as any;
  }

  // Remove all freebies from cart
  removeAllFreebies(cartItems: CartItem[]): CartItem[] {
    return cartItems.filter((item: any) => !item.is_freebie);
  }

  // Check if cart qualifies for a freebie
  canAddFreebie(cartAmount: number, freebie: FreebieProduct, cartItems: CartItem[]): boolean {
    return cartAmount >= freebie.amount && !this.isFreebieInCart(cartItems, freebie.id);
  }
}
